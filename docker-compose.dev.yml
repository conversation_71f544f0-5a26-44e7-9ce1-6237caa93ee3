version: '3.8'

services:
  website-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
    restart: unless-stopped
    container_name: ray-blog-dev
    volumes:
      # 挂载源代码以支持热重载
      - .:/app
      - /app/node_modules
      - /app/build
    networks:
      - website-dev-network
    command: ["pnpm", "start", "--", "--host", "0.0.0.0"]
    # 开发环境不需要严格的资源限制
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1G

networks:
  website-dev-network:
    driver: bridge
