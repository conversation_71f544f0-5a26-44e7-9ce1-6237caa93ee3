# 路由模块
> [!tip] 提示
> 本章仅适用于基于 HTTP 的应用程序。

在 HTTP 应用程序（例如 REST API）中，处理程序的路由路径由控制器声明的（可选的）前缀（位于 `@Controller` 装饰器内）与方法装饰器（如 `@Get('users')` ）中指定的任何路径拼接而成。您可以在本节中了解更多相关信息。此外，您还可以为应用程序中注册的所有路由定义全局前缀，或启用版本控制功能。

此外，在某些边缘情况下，在模块级别定义前缀（从而对该模块内注册的所有控制器生效）会非常实用。例如，假设一个 REST 应用暴露了多个不同端点，这些端点被应用中名为"Dashboard"的特定部分使用。这种情况下，与其在每个控制器中重复添加` `/dashboard` `前缀，不如使用工具模块` `RouterModule` `，如下所示：

```typescript
@Module({
  imports: [
    DashboardModule,
    RouterModule.register([
      {
        path: 'dashboard',
        module: DashboardModule,
      },
    ]),
  ],
})
export class AppModule {}
```

> [!tip] 提示
> `RouterModule` 类是从`@nestjs/core`包中导出的。

此外，您还可以定义层级结构。这意味着每个模块都可以包含` `children` `模块。子模块将继承其父模块的前缀。在以下示例中，我们将` `AdminModule` `注册为` `DashboardModule` `和` `MetricsModule` `的父模块。

```typescript
@Module({
  imports: [
    AdminModule,
    DashboardModule,
    MetricsModule,
    RouterModule.register([
      {
        path: 'admin',
        module: AdminModule,
        children: [
          {
            path: 'dashboard',
            module: DashboardModule,
          },
          {
            path: 'metrics',
            module: MetricsModule,
          },
        ],
      },
    ])
  ],
});
```

> [!tip] 提示
> 此功能需谨慎使用，过度使用可能导致代码难以长期维护。

在上面的示例中，任何注册在 `DashboardModule` 内部的控制器都将拥有额外的 `/admin/dashboard` 前缀（因为模块会递归地从父级到子级、自上而下地拼接路径）。同样地，定义在 `MetricsModule` 内部的每个控制器都会附加模块级前缀 `/admin/metrics` 。

