
```cardlink
url: https://docs.nestjs.com/guards
title: "Documentation | NestJS - A progressive Node.js framework"
description: "Nest is a framework for building efficient, scalable Node.js server-side applications. It uses progressive JavaScript, is built with TypeScript and combines elements of OOP (Object Oriented Programming), FP (Functional Programming), and FRP (Functional Reactive Programming)."
host: docs.nestjs.com
favicon: https://docs.nestjs.com/assets/favicons/favicon-32x32.png
image: https://nestjs.com/img/nest-og.png
```

# 守卫
守卫是一个用 `@Injectable()` 装饰器注解的类，它实现了 `CanActivate` 接口。

![img](https://images.liuyi.site/Guards_1.png)

守卫有单一职责。它们根据运行时存在的特定条件（如权限、角色、访问控制列表等），决定是否由路由处理程序处理给定请求。这通常被称为授权。在传统的 Express 应用程序中，授权（及其通常协作的兄弟——身份验证）通常由中间件处理。中间件是处理身份验证的不错选择，因为诸如令牌验证和向 `request` 对象附加属性等操作与特定路由上下文（及其元数据）没有紧密关联。

但中间件本质上是无感知的，它不知道调用`next()`函数后哪个处理器会被执行。而守卫则可以访问`ExecutionContext`实例，因此能确切知晓接下来要执行什么。它们的设计理念与异常过滤器、管道和拦截器类似，让你能在请求/响应周期中的精确位置插入处理逻辑，并以声明式的方式实现。这有助于保持代码的 DRY（不重复）原则和声明式风格。

>📌提示
>**==守卫会在所有中间件之后执行，但在任何拦截器或管道之前执行。==**

# 授权守卫
如前所述，授权是守卫的典型应用场景，因为特定路由应当仅在调用者（通常是经过认证的特定用户）具有足够权限时才可用。我们现在要构建的`AuthGuard`会假设用户已通过认证（因此请求头中附带了令牌）。它将提取并验证令牌，然后利用提取的信息判断是否允许继续处理该请求。

```JS
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class AuthGuard implements CanActivate {
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    return validateRequest(request);
  }
}
```

>📌提示
>如需了解如何在应用程序中实现身份验证机制的实际示例，请参阅[认证](/nodejs/NestJS/安全机制/认证%20Authentication.md)。同样地，更复杂的授权示例可查看[授权](/nodejs/NestJS/安全机制/授权.md)。

`validateRequest()` 函数内部的逻辑可根据需求简单或复杂。本示例的重点在于展示守卫如何融入请求/响应周期。

每个守卫都必须实现一个 `canActivate()` 函数。该函数应返回布尔值，指示当前请求是否被允许。它可以同步返回响应，也可以异步返回（通过 `Promise` 或 `Observable` ）。Nest 将根据返回值来控制后续操作：
- 如果返回 `true` ，请求将被处理。
- 如果返回 `false` ，Nest 将拒绝该请求。

# 执行上下文
`canActivate()` 函数接收单个参数，即 `ExecutionContext` 实例。 `ExecutionContext` 继承自 `ArgumentsHost` 。我们之前在异常过滤器章节中见过 `ArgumentsHost` 。在上面的示例中，我们只是使用了先前定义在 `ArgumentsHost` 上的相同辅助方法，来获取 `Request` 对象的引用。关于此主题的更多内容，您可以回顾[<font color="#ff0000">异常过滤器</font>](/nodejs/NestJS/概述/异常过滤器.md)章节中的`参数host`部分。

通过继承 `ArgumentsHost` ， `ExecutionContext` 还新增了几个辅助方法，这些方法提供了关于当前执行过程的额外细节。这些细节有助于构建更通用的守卫，使其能够跨多种控制器、方法和执行上下文工作。点击[<font color="#ff0000">此处</font>](/nodejs/NestJS/基础概念/执行上下文.md)了解更多关于 `ExecutionContext` 的信息。

# 基于角色的认证
让我们构建一个功能更完善的守卫，它只允许具有特定角色的用户访问。我们将从一个基础的守卫模板开始，并在接下来的章节中逐步完善它。目前，该守卫允许所有请求通过：

```JS
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class RolesGuard implements CanActivate {
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    return true;
  }
}
```

# 绑定守卫
与管道和异常过滤器类似，守卫可以作用于控制器范围、方法范围或全局范围。下面我们使用 `@UseGuards()` 装饰器设置一个控制器范围的守卫。该装饰器可以接受单个参数或以逗号分隔的参数列表，这使您能够通过一次声明轻松应用相应的守卫集合。

```JS
@Controller('cats')
@UseGuards(RolesGuard)
export class CatsController {}
```
>📌提示
>`@UseGuards()` 装饰器是从 `@nestjs/common` 包中导入的。

在上面的例子中，我们传递了 `RolesGuard` 类（而非实例），将实例化的责任交给框架处理，从而实现了依赖注入。与管道和异常过滤器类似，我们也可以直接传递一个即时创建的实例：

```JS
@Controller('cats')
@UseGuards(new RolesGuard())
export class CatsController {}
```

上述构造将守卫附加到该控制器声明的每个处理程序上。如果我们希望守卫仅应用于单个方法，则需在方法级别应用 `@UseGuards()` 装饰器。
要设置全局守卫，请使用 Nest 应用实例的 `useGlobalGuards()` 方法：
```JS
const app = await NestFactory.create(AppModule);
app.useGlobalGuards(new RolesGuard());
```

>💡注意
>对于混合应用而言， `useGlobalGuards()` 方法默认不会为网关和微服务设置守卫（有关如何更改此行为的信息，请参阅[<font color="#ff0000">混合应用程序</font>](https://docs.nestjs.com/faq/hybrid-application)章节）。而对于"标准"（非混合）微服务应用， `useGlobalGuards()` 确实会全局挂载守卫。

全局守卫会作用于整个应用程序，涵盖每个控制器和每个路由处理器。在依赖注入方面，从任何模块外部注册的全局守卫（如上例中使用 `useGlobalGuards()` 所示）无法注入依赖项，因为这发生在任何模块上下文之外。为解决此问题，您可以通过以下构造直接从模块内部设置守卫：
```JS
import { Module } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';

@Module({
  providers: [
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
  ],
})
export class AppModule {}
```

>📌提示
>使用此方法为守卫执行依赖注入时，请注意无论该构造应用于哪个模块，守卫实际上都是全局的。应在何处进行此操作？选择定义守卫的模块（如上例中的 `RolesGuard` ）。此外， `useClass` 并非处理自定义提供程序注册的唯一方式。了解更多信息请点击[<font color="#ff0000">此处</font>](https://docs.nestjs.com/fundamentals/custom-providers)。

# 为每个处理程序设置角色
我们的 `RolesGuard` 已经可以工作，但还不够智能。我们尚未利用守卫最重要的特性——执行上下文。它还不了解角色信息，也不清楚每个处理器允许哪些角色访问。以 `CatsController` 为例，不同路由可能需要不同的权限方案：某些路由可能仅限管理员用户访问，而其他路由可能对所有人开放。如何才能以灵活且可复用的方式将角色与路由进行匹配呢？

这正是自定义元数据发挥作用的地方（了解更多请点击[<font color="#ff0000">此处</font>](/nodejs/NestJS/基础概念/执行上下文.md#反射与元数据)）。Nest 提供了通过 `Reflector.createDecorator` 静态方法创建的装饰器或内置的 `@SetMetadata()` 装饰器，将自定义元数据附加到路由处理程序的能力。

例如，让我们使用 `Reflector.createDecorator` 方法创建一个 `@Roles()` 装饰器，该装饰器会将元数据附加到处理程序上。 `Reflector` 由框架开箱即用提供，并从 `@nestjs/core` 包中导出。

```JS
import { Reflector } from '@nestjs/core';

export const Roles = Reflector.createDecorator<string[]>();
```
这里的 `Roles` 装饰器是一个函数，它接收一个类型为 `string[]` 的单一参数。
现在，要使用这个装饰器，我们只需用它来注解处理程序：

```typescript
@Post()
@Roles(['admin'])
async create(@Body() createCatDto: CreateCatDto) {
  this.catsService.create(createCatDto);
}
```
我们已将 `Roles` 装饰器元数据附加到 `create()` 方法上，表明只有拥有 `admin` 角色的用户才被允许访问此路由。

或者，我们也可以不使用 `Reflector.createDecorator` 方法，而是使用内置的 `@SetMetadata()` 装饰器。点击[<font color="#ff0000">此处</font>](https://docs.nestjs.com/fundamentals/execution-context#reflection-and-metadata)了解更多信息。

# 完整实现示例
现在让我们回过头来，将这部分内容与我们的 `RolesGuard` 联系起来。目前，它只是简单地返回 `true` ，允许所有请求继续执行。我们希望根据当前用户的角色与当前处理路由所需角色进行比较，从而有条件地决定返回值。为了访问路由的角色（自定义元数据），我们将再次使用 `Reflector` 辅助类，具体如下：
```typescript
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Roles } from './roles.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const roles = this.reflector.get(Roles, context.getHandler());
    if (!roles) {
      return true;
    }
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    return matchRoles(roles, user.roles);
  }
}
```

>📌提示
>在 Node.js 领域，通常会将授权用户附加到 `request` 对象上。因此，在我们上面的示例代码中，我们假设 `request.user` 包含用户实例和允许的角色。在您的应用程序中，您可能会在自定义的身份验证守卫（或中间件）中建立这种关联。有关此主题的更多信息，请查阅[<font color="#ff0000">认证</font>](/nodejs/NestJS/安全机制/认证%20Authentication.md)。

>⚠️警告
>`matchRoles()` 函数内部的逻辑可以根据需要简单或复杂。这个示例的主要目的是展示守卫如何融入请求/响应周期。

有关如何在上下文敏感方式中使用 `Reflector` 的更多详情，请参阅执行上下文章节的[<font color="#ff0000">反射与元数据</font>](/nodejs/NestJS/基础概念/执行上下文.md#反射与元数据)部分。

当权限不足的用户请求某个端点时，Nest 会自动返回以下响应：

```typescript
{
  "statusCode": 403,
  "message": "Forbidden resource",
  "error": "Forbidden"
}
```

需要注意的是，当守卫返回 `false` 时，框架内部会抛出 `ForbiddenException` 。如果你想返回不同的错误响应，应该抛出自己特定的异常。例如：
```JS
throw new UnauthorizedException();
```
守卫抛出的任何异常都将由[<font color="#ff0000">异常处理层</font>](/nodejs/NestJS/概述/异常过滤器.md)处理（全局异常过滤器以及应用于当前上下文的任何异常过滤器）。

>📌提示
>如果您想了解如何实现授权的实际案例，请查阅[<font color="#ff0000">授权</font>](/nodejs/NestJS/安全机制/授权.md)。

