# 异常过滤器
Nest 内置了一个异常处理层，负责处理应用程序中所有未捕获的异常。当应用程序代码未处理某个异常时，该异常会被这个处理层捕获，然后自动返回一个用户友好的响应。

![img](https://images.liuyi.site/Filter_1.png)

开箱即用，这个功能由内置的全局异常过滤器实现，它能处理 `HttpException` 类型（及其子类）的异常。当遇到无法识别的异常（既不是 `HttpException` 也不是继承自 `HttpException` 的类）时，内置异常过滤器会生成以下默认 JSON 响应：

```json
{
  "statusCode": 500,
  "message": "Internal server error"
}
```

> [!tip] 提示
> 全局异常过滤器部分支持 `http-errors` 库。基本上，任何包含 `statusCode` 和 `message` 属性的抛出异常都会被正确解析并作为响应返回（而不是像处理无法识别异常那样返回默认的 `InternalServerErrorException` ）。

# 抛出标准异常
Nest 提供了一个内置的 `HttpException` 类，该类从 `@nestjs/common` 包中导出。对于典型的基于 HTTP REST/GraphQL API 的应用程序，最佳实践是在发生特定错误条件时发送标准的 HTTP 响应对象。

例如，在 `CatsController` 中，我们有一个 `findAll()` 方法（一个 `GET` 路由处理器）。假设这个路由处理器由于某种原因抛出了异常。为了演示这一点，我们将硬编码如下：

`cats.controller.ts`
```typescript
@Get()
async findAll() {
  throw new HttpException('Forbidden', HttpStatus.FORBIDDEN);
}
```

> [!tip] 提示
> 我们在此使用了 `HttpStatus` ，这是一个从 `@nestjs/common` 包导入的辅助枚举类型。

当客户端调用此端点时，响应格式如下所示：

```json
{
  "statusCode": 403,
  "message": "Forbidden"
}
```

`HttpException` 构造函数接收两个必填参数，这两个参数决定了响应内容：
- `statusCode` ：默认为 `status` 参数提供的 HTTP 状态码
- `message` ：基于 `status` 的 HTTP 错误简短描述

若只需覆盖 JSON 响应体中的消息部分，请在 `response` 参数中传入字符串。若要覆盖整个 JSON 响应体，则在 `response` 参数中传入对象。Nest 会将该对象序列化为 JSON 响应体返回。

第二个构造参数 `status` 应为有效的 HTTP 状态码。最佳实践是使用从 `@nestjs/common` 导入的 `HttpStatus` 枚举值。

第三个构造参数（可选） `options` 可用于提供错误原因。该 `cause` 对象不会被序列化到响应对象中，但可用于日志记录目的，为引发 `HttpException` 的内部错误提供有价值的信息。

以下是覆盖整个响应体并提供错误原因的示例：

`cats.controller.ts`
```typescript
@Get()
async findAll() {
  try {
    await this.service.findAll()
  } catch (error) {
    throw new HttpException({
      status: HttpStatus.FORBIDDEN,
      error: 'This is a custom message',
    }, HttpStatus.FORBIDDEN, {
      cause: error
    });
  }
}
```

使用上述代码，响应将如下所示：

```json
{
  "status": 403,
  "error": "This is a custom message"
}
```

# 异常日志记录

默认情况下，异常过滤器不会记录像 `HttpException` 这样的内置异常（以及任何继承自它的异常）。当这些异常被抛出时，它们不会出现在控制台中，因为它们被视为正常应用程序流程的一部分。同样的行为也适用于其他内置异常，例如 `WsException` 和 `RpcException` 。

这些异常都继承自基础类 `IntrinsicException` ，该类从 `@nestjs/common` 包中导出。这个基类有助于区分属于正常应用程序操作的异常与不属于正常操作的异常。

若需记录这些异常，您可以创建自定义异常过滤器。我们将在下一节中说明具体操作方法。

# 自定义异常

在许多情况下，您无需编写自定义异常，可以直接使用内置的 Nest HTTP 异常，如下一节所述。若确实需要创建定制化异常，最佳实践是建立自己的异常层次结构，让自定义异常继承自基类 `HttpException` 。通过这种方式，Nest 能识别您的异常，并自动处理错误响应。让我们来实现这样一个自定义异常：

`forbidden.exception.ts`
```typescript
export class ForbiddenException extends HttpException {
  constructor() {
    super('Forbidden', HttpStatus.FORBIDDEN);
  }
}
```

由于 `ForbiddenException` 继承自基础 `HttpException` ，它能与内置异常处理器无缝协作，因此我们可以在 `findAll()` 方法内部使用它。

`cats.controller.ts`
```typescript
@Get()
async findAll() {
  throw new ForbiddenException();
}
```

# 内置 HTTP 异常

Nest 提供了一组继承自基础 `HttpException` 的标准异常类。这些异常类从 `@nestjs/common` 包中导出，代表了大多数常见的 HTTP 异常情况：

- `BadRequestException`
- `UnauthorizedException`
- `NotFoundException`
- `ForbiddenException`
- `NotAcceptableException`
- `RequestTimeoutException`
- `ConflictException`
- `GoneException`
- `HttpVersionNotSupportedException`
- `PayloadTooLargeException`
- `UnsupportedMediaTypeException`
- `UnprocessableEntityException`
- `InternalServerErrorException`
- `NotImplementedException`
- `ImATeapotException`
- `MethodNotAllowedException`
- `BadGatewayException`
- `ServiceUnavailableException`
- `GatewayTimeoutException`
- `PreconditionFailedException`

所有内置异常都可以通过 `options` 参数同时提供错误 `cause` 和错误描述：

```typescript
throw new BadRequestException('Something bad happened', {
  cause: new Error(),
  description: 'Some error description',
});
```

使用上述方法，响应将呈现如下形式：
```json
{
  "message": "Something bad happened",
  "error": "Some error description",
  "statusCode": 400
}
```

# 异常过滤器
虽然基础（内置）异常过滤器能自动处理多数情况，但您可能需要对异常层进行完全控制。例如，您可能希望添加日志记录功能，或基于某些动态因素使用不同的 JSON 结构。异常过滤器正是为此而设计，它允许您精确控制流程以及返回给客户端的响应内容。

让我们创建一个异常过滤器，专门捕获属于 `HttpException` 类实例的异常，并为它们实现自定义响应逻辑。为此，我们需要访问底层平台的 `Request` 和 `Response` 对象。我们将获取 `Request` 对象以便提取原始 `url` 信息并纳入日志记录，同时使用 `Response` 对象通过 `response.json()` 方法直接控制发送的响应内容。

`http-exception.filter.ts`
```typescript
import { ExceptionFilter, Catch, ArgumentsHost, HttpException } from '@nestjs/common';
import { Request, Response } from 'express';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();

    response
      .status(status)
      .json({
        statusCode: status,
        timestamp: new Date().toISOString(),
        path: request.url,
      });
  }
}
```

> [!tip] 提示
> 所有异常过滤器都应实现通用的 `ExceptionFilter<T>` 接口。这要求您提供具有指定签名的 `catch(exception: T, host: ArgumentsHost)` 方法。 `T` 表示异常的类型。

> [!warning] 警告
> 如果你正在使用 `@nestjs/platform-fastify` ，可以用 `response.send()` 替代 `response.json()` 。别忘了从 `fastify` 导入正确的类型。

`@Catch(HttpException)` 装饰器将所需的元数据绑定到异常过滤器上，告知 Nest 这个特定过滤器仅处理 `HttpException` 类型的异常。 `@Catch()` 装饰器可接收单个参数或以逗号分隔的列表，使您能够一次性为多种异常类型设置过滤器。

# 参数宿主
让我们来看看 `catch()` 方法的参数。 `exception` 参数是当前正在处理的异常对象。 `host` 参数是一个 `ArgumentsHost` 对象。 `ArgumentsHost` 是一个强大的实用工具对象，我们将在[执行上下文章节](https://docs.nestjs.com/fundamentals/execution-context)中进一步研究。在这个代码示例中，我们使用它来获取对原始请求处理程序（异常发生的控制器中）传递的 `Request` 和 `Response` 对象的引用。在此代码示例中，我们在 `ArgumentsHost` 上使用了一些辅助方法来获取所需的 `Request` 和 `Response` 对象。点击[此处](https://docs.nestjs.com/fundamentals/execution-context)了解更多关于 `ArgumentsHost` 的信息。

这种抽象层级的原因在于 `ArgumentsHost` 函数适用于所有上下文环境（例如我们当前使用的 HTTP 服务器上下文，还包括微服务和 WebSockets）。在执行上下文章节中，我们将了解如何借助 `ArgumentsHost` 及其辅助函数来访问任意执行上下文的底层参数。这将使我们能够编写适用于所有上下文的通用异常过滤器。

# 绑定过滤器
让我们将新的 `HttpExceptionFilter` 绑定到 `CatsController` 的 `create()` 方法上。

`cats.controller.ts`
```typescript
@Post()
@UseFilters(new HttpExceptionFilter())
async create(@Body() createCatDto: CreateCatDto) {
  throw new ForbiddenException();
}
```

> [!tip] 提示
> `@UseFilters()` 装饰器是从 `@nestjs/common` 包中导入的。

我们在此处使用了 `@UseFilters()` 装饰器。与 `@Catch()` 装饰器类似，它可以接收单个过滤器实例，或以逗号分隔的过滤器实例列表。这里我们直接创建了 `HttpExceptionFilter` 的实例。或者，你也可以传递类（而非实例），将实例化的责任交给框架处理，并启用依赖注入功能。

`cats.controller.ts`
```typescript
@Post()
@UseFilters(HttpExceptionFilter)
async create(@Body() createCatDto: CreateCatDto) {
  throw new ForbiddenException();
}
```

> [!tip] 提示
> 尽可能使用类而非实例来应用过滤器。由于 Nest 可以在整个模块中轻松复用同一类的实例，这种方式能有效降低内存消耗。

在上面的示例中， `HttpExceptionFilter` 仅应用于单个 `create()` 路由处理程序，使其具有方法作用域。异常过滤器可以作用于不同层级：控制器/解析器/网关的方法作用域、控制器作用域或全局作用域。例如，要将过滤器设置为控制器作用域，您可以执行以下操作：

`cats.controller.ts`
```typescript
@Controller()
@UseFilters(new HttpExceptionFilter())
export class CatsController {}
```

这种结构为 `CatsController` 内部定义的每个路由处理程序设置了 `HttpExceptionFilter` 。

要创建一个全局作用域的过滤器，你需要执行以下操作：

`main.ts`
```typescript
async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.useGlobalFilters(new HttpExceptionFilter());
  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
```

> [!warning] 警告
> `useGlobalFilters()` 方法不会为网关或混合应用设置过滤器。

全局作用域过滤器适用于整个应用程序，为每个控制器和每个路由处理器提供服务。在依赖注入方面，从任何模块外部注册的全局过滤器（如上例中使用 `useGlobalFilters()` 所示）无法注入依赖项，因为这发生在任何模块的上下文之外。为了解决这个问题，您可以使用以下结构直接从任何模块注册全局作用域过滤器：

<span id="span1">`app.module.ts`</span>

```typescript
import { Module } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';

@Module({
  providers: [
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
  ],
})
export class AppModule {}
```

> [!tip] 提示
> 使用这种方式为过滤器执行依赖注入时，请注意无论该构造应用于哪个模块，过滤器实际上都是全局的。应该在何处进行此操作？选择定义过滤器的模块（上例中的 `HttpExceptionFilter` ）。此外， `useClass` 并非处理自定义提供程序注册的唯一方式。点击此处了解更多。

你可以根据需要添加任意数量的过滤器，只需将它们逐个添加到 providers 数组中即可。

# 捕获所有异常

为了捕获所有未处理的异常（无论异常类型如何），请将 `@Catch()` 装饰器的参数列表留空，例如 `@Catch()` 。

在下面的示例中，我们有一段与平台无关的代码，因为它使用 HTTP 适配器来传递响应，并且不直接使用任何平台特定的对象（ `Request` 和 `Response` ）：

```typescript
import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';

@Catch()
export class CatchEverythingFilter implements ExceptionFilter {
  constructor(private readonly httpAdapterHost: HttpAdapterHost) {}

  catch(exception: unknown, host: ArgumentsHost): void {
    // In certain situations `httpAdapter` might not be available in the
    // constructor method, thus we should resolve it here.
    const { httpAdapter } = this.httpAdapterHost;

    const ctx = host.switchToHttp();

    const httpStatus =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    const responseBody = {
      statusCode: httpStatus,
      timestamp: new Date().toISOString(),
      path: httpAdapter.getRequestUrl(ctx.getRequest()),
    };

    httpAdapter.reply(ctx.getResponse(), responseBody, httpStatus);
  }
}
```

> [!warning] 警告
> 当将捕获所有异常的过滤器与绑定到特定类型的过滤器结合使用时，应首先声明"捕获所有"过滤器，以便特定过滤器能正确处理其绑定的类型。

# 继承

通常情况下，您会创建完全自定义的异常过滤器来满足应用程序需求。但在某些场景下，您可能希望直接扩展内置的默认全局异常过滤器，并根据特定因素覆盖其行为。

为了将异常处理委托给基础过滤器，您需要继承 `BaseExceptionFilter` 并调用继承的 `catch()` 方法。

`all-exceptions.filter.ts`
```typescript
import { Catch, ArgumentsHost } from '@nestjs/common';
import { BaseExceptionFilter } from '@nestjs/core';

@Catch()
export class AllExceptionsFilter extends BaseExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    super.catch(exception, host);
  }
}
```

> [!warning] 警告
> 扩展 `BaseExceptionFilter` 的方法作用域和控制器作用域过滤器不应使用 `new` 进行实例化。相反，应让框架自动实例化它们。

全局过滤器可以扩展基础过滤器。这可以通过以下两种方式之一实现。

第一种方法是在实例化自定义全局过滤器时注入 `HttpAdapter` 引用：

```typescript
async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  const { httpAdapter } = app.get(HttpAdapterHost);
  app.useGlobalFilters(new AllExceptionsFilter(httpAdapter));

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
```

第二种方法是使用[此处](#span1)所示的 `APP_FILTER` 令牌。