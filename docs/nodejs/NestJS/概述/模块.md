

# 模块
模块是一个使用`@Module` 装饰器注解的类。该装饰器提供的元数据，使得 Nest 能够高效地组织和管理应用程序结构。
![img](https://images.liuyi.site/Modules_1.png)
每个 Nest 应用程序至少有一个模块，即根模块，它是 Nest 构建应用程序图的起点。这个图是 Nest 用来解析模块和提供者之间关系和依赖关系的内部结构。虽然小型应用可能仅包含根模块，但通常情况并非如此。我们强烈建议将模块作为组织组件的有效方式。对于大多数应用而言，通常会包含多个模块，每个模块封装一组紧密相关的功能。

`@Module()` 装饰器接收一个包含模块描述属性的对象：

|   |   |
|---|---|
|`providers`|将由 Nest 注入器实例化且至少可在本模块内共享的提供者 <br/> <span style={{backgroundColor:'#fff88f'}}>可以简单理解为本模块里有Nest进行注入实例化的bean</span>|
|`controllers`|本模块中定义且需要被实例化的控制器集合|
|`imports`|导出本模块所需提供者的导入模块列表 <br/> <span style={{backgroundColor:'#fff88f'}}>即本模块的bean在注入实例化的时候，需要使用到的其他的模块的bean示例（或者就是其他的一整个模块）</span>|
|`exports`|该模块提供的 `providers` 子集，这些子集应当对导入该模块的其他模块可用。您既可以使用提供者本身，也可以仅使用其令牌（ `provide` 值）<br/><span style={{backgroundColor:'#fff88f'}}>表示可以给其他模块注入使用的bean</span>|

模块默认会封装提供者，这意味着您只能注入属于当前模块或从其他导入模块显式导出的提供者。模块导出的提供者本质上构成了该模块的公共接口或 API。

# 功能模块
在我们的示例中， `CatsController` 和 `CatsService` 密切相关且服务于同一应用领域。将它们分组到功能模块中是合理的做法。功能模块用于组织与特定功能相关的代码，有助于维持清晰的边界和更好的组织结构。随着应用程序或团队规模的增长，这一点尤为重要，同时也符合 SOLID 原则。

接下来，我们将创建 `CatsModule` 来演示如何将控制器和服务进行分组。
`cats/cats.module.ts`
```typescript
import { Module } from '@nestjs/common';
import { CatsController } from './cats.controller';
import { CatsService } from './cats.service';

@Module({
  controllers: [CatsController],
  providers: [CatsService],
})
export class CatsModule {}
```
> [!tip] 提示
> 要使用 CLI 创建模块，只需执行 `$ nest g module cats` 命令。

如上所述，我们在 `cats.module.ts` 文件中定义了 `CatsModule` ，并将与该模块相关的所有内容移至 `cats` 目录。最后需要做的是将该模块导入根模块（即 `AppModule` ，定义在 `app.module.ts` 文件中）。

`app.module.ts`
```typescript
import { Module } from '@nestjs/common';
import { CatsModule } from './cats/cats.module';

@Module({
  imports: [CatsModule],
})
export class AppModule {}
```

当前我们的目录结构如下所示：

```plaintext
src
 |
 |---cats 
 |     |
 |     |---dto 
 |     |    |
 |     |    |---create-cat.dto.ts
 |     |
 |     |---interfaces
 |     |    |
 |     |    |---cat.interface.ts
 |     |
 |     |---cats.controller.ts
 |     |---cats.module.ts
 |     |---cats.service.ts
 |
 |-----app.module.ts
 |-----main.ts
```

# 共享模块
在 Nest 中，模块默认是单例模式，因此您可以轻松地在多个模块之间共享同一个提供者的实例。

![img](https://images.liuyi.site/Shared_Module_1.png)

每个模块自动成为共享模块。一旦创建，它就可以被任何模块重复使用。假设我们想要在多个其他模块之间共享 `CatsService` 的实例。为了实现这一点，我们首先需要通过将 `CatsService` 提供者添加到模块的 `exports` 数组中来导出它，如下所示：

`cats.module.ts`
```typescript
import { Module } from '@nestjs/common';
import { CatsController } from './cats.controller';
import { CatsService } from './cats.service';

@Module({
  controllers: [CatsController],
  providers: [CatsService],
  exports: [CatsService]
})
export class CatsModule {}
```
现在任何导入 `CatsModule` 的模块都可以访问 `CatsService` ，并且会与其他所有导入该模块的模块共享同一个实例。

如果我们在每个需要 `CatsService` 的模块中直接注册它，确实可以工作，但这会导致每个模块都获得自己独立的 `CatsService` 实例。由于创建了同一服务的多个实例，这可能导致内存使用量增加，如果服务维护任何内部状态，还可能引发意外行为，例如状态不一致。

通过将 `CatsService` 封装在模块（例如 `CatsModule` ）中并导出，我们确保所有导入 `CatsModule` 的模块都能复用同一个 `CatsService` 实例。这不仅降低了内存消耗，还使得行为更加可预测——由于所有模块共享同一实例，共享状态或资源的管理变得更加容易。这正是 NestJS 等框架中模块化与依赖注入的核心优势之一，它允许服务在整个应用中被高效共享。

# 模块再导出
如上所述，模块可以导出其内部提供者。此外，它们还可以再导出所导入的模块。在下面的示例中， `CommonModule` 既被导入到 `CoreModule` 中，又从 `CoreModule` 中导出，使得导入该模块的其他模块也能使用它。

```typescript
@Module({
  imports: [CommonModule],
  exports: [CommonModule],
})
export class CoreModule {}
```

# 依赖注入

模块类也可以注入提供者（例如用于配置目的）：

`cats.module.ts`
```typescript
import { Module } from '@nestjs/common';
import { CatsController } from './cats.controller';
import { CatsService } from './cats.service';

@Module({
  controllers: [CatsController],
  providers: [CatsService],
})
export class CatsModule {
  constructor(private catsService: CatsService) {}
}
```

然而，由于`循环依赖`问题，模块类本身无法作为提供者被注入。

# 全局模块
如果必须在各处重复导入相同的模块集合，这会变得非常繁琐。与 Nest 不同，Angular 的模块注册在全局作用域中，一旦定义即可随处使用。而 Nest 则将提供者封装在模块作用域内，若不先导入封装模块，就无法在其他地方使用该模块的提供者。

当需要提供一组开箱即用的全局提供者（例如辅助工具、数据库连接等）时，可以使用`@Global` 装饰器将模块设为全局模块。

```typescript
import { Module, Global } from '@nestjs/common';
import { CatsController } from './cats.controller';
import { CatsService } from './cats.service';

@Global()
@Module({
  controllers: [CatsController],
  providers: [CatsService],
  exports: [CatsService],
})
export class CatsModule {}
```

`@Global()` 装饰器使模块具有全局作用域。全局模块通常只需注册一次，一般由根模块或核心模块完成。在上面的示例中， `CatsService` 提供者将无处不在，希望注入该服务的模块无需在其 imports 数组中导入 `CatsModule` 。

> [!tip] 提示
> 将一切设为全局并非推荐的设计实践。虽然全局模块可以减少样板代码，但通常更好的做法是使用 `imports` 数组以受控且清晰的方式使模块 API 可供其他模块使用。这种方法能提供更好的结构和可维护性，确保只共享模块的必要部分，同时避免应用程序无关部分之间产生不必要的耦合。

# 动态模块
Nest 中的动态模块允许您创建可在运行时配置的模块。当您需要提供灵活、可定制的模块（其中提供者可以根据特定选项或配置创建）时，这尤其有用。以下是动态模块工作原理的简要概述。

```typescript
import { Module, DynamicModule } from '@nestjs/common';
import { createDatabaseProviders } from './database.providers';
import { Connection } from './connection.provider';

@Module({
  providers: [Connection],
  exports: [Connection],
})
export class DatabaseModule {
  static forRoot(entities = [], options?): DynamicModule {
    const providers = createDatabaseProviders(options, entities);
    return {
      module: DatabaseModule,
      providers: providers,
      exports: providers,
    };
  }
}
```

> [!tip] 提示
> `forRoot()` 方法可以同步或异步（即通过 `Promise` ）返回一个动态模块。

该模块默认定义了 `Connection` 提供者（通过 `@Module()` 装饰器元数据），此外——根据传入 `forRoot()` 方法的 `entities` 和 `options` 对象——还会暴露一系列提供者集合，例如存储库。请注意动态模块返回的属性会扩展（而非覆盖） `@Module()` 装饰器中定义的基础模块元数据。正是通过这种方式，静态声明的 `Connection` 提供者与动态生成的存储库提供者都能从该模块导出。

若要在全局范围内注册动态模块，请将 `global` 属性设置为 `true` 。

```typescript
{
  global: true,
  module: DatabaseModule,
  providers: providers,
  exports: providers,
}
```

> [!warning]
> 正如前文所述，将所有内容设为全局并非明智的设计决策。

`DatabaseModule` 可通过以下方式导入并配置：

```typescript
import { Module } from '@nestjs/common';
import { DatabaseModule } from './database/database.module';
import { User } from './users/entities/user.entity';

@Module({
  imports: [DatabaseModule.forRoot([User])],
})
export class AppModule {}
```

若需重新导出动态模块，可在 exports 数组中省略 `forRoot()` 方法调用：
```typescript
import { Module } from '@nestjs/common';
import { DatabaseModule } from './database/database.module';
import { User } from './users/entities/user.entity';

@Module({
  imports: [DatabaseModule.forRoot([User])],
  exports: [DatabaseModule],
})
export class AppModule {}
```

《[动态模块](https://docs.nestjs.com/fundamentals/dynamic-modules)》章节对此主题进行了更详细的探讨，并包含一个实际示例。

> [!tip] 提示
> 了解如何在[本章](https://docs.nestjs.com/fundamentals/dynamic-modules#configurable-module-builder)中使用 `ConfigurableModuleBuilder` 构建高度可定制的动态模块。


:::tip

了解如何在[本章](https://docs.nestjs.com/fundamentals/dynamic-modules#configurable-module-builder)中使用 `ConfigurableModuleBuilder` 构建高度可定制的动态模块。

:::