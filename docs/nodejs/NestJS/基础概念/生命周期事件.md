
```cardlink
url: https://docs.nestjs.com/fundamentals/lifecycle-events
title: "Documentation | NestJS - A progressive Node.js framework"
description: "Nest is a framework for building efficient, scalable Node.js server-side applications. It uses progressive JavaScript, is built with TypeScript and combines elements of OOP (Object Oriented Programming), FP (Functional Programming), and FRP (Functional Reactive Programming)."
host: docs.nestjs.com
favicon: https://docs.nestjs.com/assets/favicons/favicon-32x32.png
image: https://nestjs.com/img/nest-og.png
```


# 生命周期事件
Nest 应用程序及其每个组成元素都拥有由 Nest 管理的生命周期。Nest 提供了生命周期钩子，使开发者能够洞察关键生命周期事件，并在这些事件发生时执行相应操作（在模块、提供者或控制器上运行注册的代码）。

# 生命周期序列
下图描绘了从应用启动到 Node 进程退出期间，关键应用生命周期事件的顺序。我们可以将整个生命周期划分为三个阶段：初始化阶段、运行阶段和终止阶段。利用这个生命周期，您可以规划模块和服务的适当初始化，管理活动连接，并在应用收到终止信号时优雅地关闭它。

![img](https://images.liuyi.site/lifecycle-events.png)

# 生命周期事件
生命周期事件发生在应用启动和关闭期间。Nest 会在以下每个生命周期事件中调用模块、提供者和控制器上注册的生命周期钩子方法（需要先启用关闭钩子，如下所述）。如上图所示，Nest 还会调用适当的底层方法来开始监听连接和停止监听连接。

在下表中， `onModuleInit` 和 `onApplicationBootstrap` 仅当您显式调用 `app.init()` 或 `app.listen()` 时才会触发。

在下表中， `onModuleDestroy` 、 `beforeApplicationShutdown` 和 `onApplicationShutdown` 仅在你显式调用 `app.close()` 或进程收到特殊系统信号（如 SIGTERM）且你在应用启动时正确调用了 `enableShutdownHooks` 时才会触发（参见下文"[应用关闭](#应用关闭)"部分）。

|生命周期钩子方法 | 触发钩子方法调用的生命周期事件|
|-|-|
|onModuleInit()|当宿主模块的依赖项解析完成后调用。|
|onApplicationBootstrap()|在所有模块初始化完成后调用，但在开始监听连接之前。|
|onModuleDestroy()|在接收到终止信号（例如 `SIGTERM` ）后调用。|
|beforeApplicationShutdown()|在所有 `onModuleDestroy()` 处理程序完成后调用（Promise 已解决或拒绝）；<br/> 一旦完成（Promise 已解决或拒绝），所有现有连接将被关闭（调用 `app.close()` ）。|
|onApplicationShutdown()|在连接关闭后调用（ `app.close()` 解析完成时触发）。|

❗️对于这些事件，若未显式调用 `app.close()` ，则必须主动启用才能使它们响应系统信号（如 `SIGTERM` ）。详见下文"[应用关闭](#应用关闭)"章节。

> ⚠️警告
> 上述生命周期钩子不适用于请求作用域的类。请求作用域类与应用程序生命周期无关，其生存周期不可预测。它们专为每个请求创建，并在响应发送后自动被垃圾回收。

>📌提示
>`onModuleInit()` 和 `onApplicationBootstrap()` 的执行顺序直接取决于模块导入顺序，且会等待前一个钩子完成。

# 用法
每个生命周期钩子都对应一个接口。从技术上讲，这些接口是可选的，因为在 TypeScript 编译后它们就不存在了。尽管如此，为了获得强类型和编辑器工具的支持，最好还是使用它们。要注册一个生命周期钩子，需要实现相应的接口。例如，要在特定类（如控制器、提供者或模块）上注册一个在模块初始化期间调用的方法，可以通过提供 `onModuleInit()` 方法来实现 `OnModuleInit` 接口，如下所示：

```JS

import { Injectable, OnModuleInit } from '@nestjs/common';

@Injectable()
export class UsersService implements OnModuleInit {
  onModuleInit() {
    console.log(`The module has been initialized.`);
  }
}

```

# 异步初始化
`OnModuleInit` 和 `OnApplicationBootstrap` 钩子都允许您延迟应用程序初始化过程（返回一个 `Promise` 或将方法标记为 `async` ，并在方法体内 `await` 异步方法完成）。

```JS
async onModuleInit(): Promise<void> {
  await this.fetch();
}
```

# 应用关闭
onModuleDestroy()`、`beforeApplicationShutdown()`和onApplicationShutdown()`钩子会在终止阶段被调用（响应显式的`app.close()`调用，或当选择接收 SIGTERM 等系统信号时）。该特性常与 Kubernetes 配合管理容器生命周期，也被 Heroku 用于 dynos 或类似服务。

停机钩子监听器会消耗系统资源，因此默认处于禁用状态。要使用关闭钩子，必须通过调用 `enableShutdownHooks()` 来启用监听器。

```JS
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Starts listening for shutdown hooks
  app.enableShutdownHooks();

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
```

> ⚠️警告
> 由于平台固有局限性，NestJS 在 Windows 系统上对应用关闭钩子的支持有限。您可以预期 `SIGINT` 能正常工作， `SIGBREAK` 也能运行， `SIGHUP` 在某种程度上也可使用——[详见说明](https://nodejs.org/api/process.html#process_signal_events
)。但 `SIGTERM` 在 Windows 上永远无法生效，因为通过任务管理器终止进程是无条件的，"即应用程序无法检测或阻止此操作"。以下是 libuv 的[相关文档](https://docs.libuv.org/en/v1.x/signal.html)，可深入了解 `SIGINT` 、 `SIGBREAK` 等信号在 Windows 上的处理机制。另请参阅 Node.js 的[进程信号事件](https://nodejs.org/api/process.html#process_signal_events)文档。

> ✏️信息
> `enableShutdownHooks` 通过启动监听器消耗内存。当您在单个 Node 进程中运行多个 Nest 应用时（例如使用 Jest 运行并行测试），Node 可能会抱怨监听器进程过多。因此， `enableShutdownHooks` 默认未启用。在单个 Node 进程中运行多个实例时，请注意这种情况。

当应用程序接收到终止信号时，它将按照上述顺序调用所有已注册的 `onModuleDestroy()` 、 `beforeApplicationShutdown()` ，然后是 `onApplicationShutdown()` 方法，并将相应信号作为第一个参数传入。如果注册的函数等待异步调用（返回 promise），Nest 将等待该 promise 被 resolve 或 reject 后才会继续执行后续序列。

```JS
@Injectable()
class UsersService implements OnApplicationShutdown {
  onApplicationShutdown(signal: string) {
    console.log(signal); // e.g. "SIGINT"
  }
}
```

> ✏️信息
> 调用 `app.close()` 不会终止 Node 进程，只会触发 `onModuleDestroy()` 和 `onApplicationShutdown()` 钩子，因此如果存在某些定时器、长时间运行的后台任务等，进程不会自动终止。
