{"name": "ray-blog", "version": "0.0.1", "private": true, "engines": {"node": ">=18.0"}, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "server": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc", "lint": "eslint --ext .js,.jsx,.ts,.tsx --fix --quiet src", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,md,mdx,json,css,scss,html,yml,yaml}\""}, "dependencies": {"@docusaurus/core": "3.8.1", "@docusaurus/plugin-content-docs": "^3.8.1", "@docusaurus/preset-classic": "3.8.1", "@docusaurus/theme-mermaid": "^3.8.1", "@iconify/react": "^6.0.0", "@mdx-js/react": "^3.0.0", "clsx": "^2.0.0", "docusaurus-lunr-search": "^3.6.0", "framer-motion": "^12.23.12", "lunr": "^2.3.9", "lunr-languages": "^1.14.0", "prism-react-renderer": "^2.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-popper": "^2.3.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@antfu/eslint-config": "^5.2.1", "@docusaurus/module-type-aliases": "3.8.1", "@docusaurus/tsconfig": "3.8.1", "@docusaurus/types": "3.8.1", "@eslint/eslintrc": "^3.3.1", "@node-rs/jieba": "^2.0.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-perfectionist": "^4.15.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-unused-imports": "^4.2.0", "prettier": "^3.6.2", "typescript": "~5.6.2"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}}