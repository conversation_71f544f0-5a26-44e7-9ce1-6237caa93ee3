# Docker 部署说明

本项目已配置 Docker 和 Docker Compose，可以轻松部署和运行。

## 文件说明

- `Dockerfile`: Docker 镜像构建配置（多阶段构建）
- `docker-compose.yml`: Docker Compose 服务编排配置
- `.dockerignore`: Docker 构建时忽略的文件和目录

## 快速开始

### 使用 Docker Compose

1. 构建并启动服务：
```bash
docker-compose up -d
```

2. 查看服务状态：
```bash
docker-compose ps
```

3. 查看日志：
```bash
docker-compose logs -f website
```

4. 停止服务：
```bash
docker-compose down
```

5. 重启服务：
```bash
docker-compose restart
```

### 使用 Docker 命令

1. 构建镜像：
```bash
docker build -t ray-blog .
```

2. 运行容器：
```bash
docker run -d -p 3100:3100 --name ray-blog ray-blog
```

3. 查看容器状态：
```bash
docker ps
```

4. 查看日志：
```bash
docker logs -f ray-blog
```

5. 停止并删除容器：
```bash
docker stop ray-blog
docker rm ray-blog
```

## 访问应用

应用启动后，可以通过以下地址访问：
- 本地访问：http://localhost:3100
- 服务器访问：http://your-server-ip:3100

## 配置说明

### 端口配置
- 默认端口：3100
- 如需修改端口，请同时修改 `docker-compose.yml` 文件中的端口映射

### 环境变量
- `NODE_ENV=production`: 设置为生产环境

### 数据持久化
- 日志目录：`./logs` 映射到容器内的 `/app/logs`
- 如需添加其他持久化目录，请在 `docker-compose.yml` 中的 volumes 部分添加

### 资源限制
- CPU 限制：1.0 核心，内存限制：512MB
- CPU 预留：0.5 核心，内存预留：256MB

### 健康检查
- 配置了健康检查，每 30 秒检查一次服务状态
- 启动后 40 秒开始检查，超时时间 10 秒，重试 3 次

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :3100
   # 或者修改 docker-compose.yml 中的端口映射
   ```

2. **构建失败**
   ```bash
   # 清理 Docker 缓存
   docker system prune -a
   # 重新构建
   docker-compose build --no-cache
   ```

3. **容器无法启动**
   ```bash
   # 查看详细日志
   docker-compose logs website
   ```



## 生产部署建议

1. **使用多阶段构建优化镜像大小**
2. **配置反向代理（如 Nginx）**
3. **设置健康检查**
4. **配置日志轮转**
5. **使用 Docker Secrets 管理敏感信息**
