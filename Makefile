# Docker 管理命令

.PHONY: help build up down logs restart clean dev dev-down dev-logs

# 默认目标
help:
	@echo "可用命令:"
	@echo "  build     - 构建 Docker 镜像"
	@echo "  up        - 启动生产环境服务"
	@echo "  down      - 停止生产环境服务"
	@echo "  logs      - 查看生产环境日志"
	@echo "  restart   - 重启生产环境服务"
	@echo "  clean     - 清理 Docker 资源"
	@echo "  dev       - 启动开发环境服务"
	@echo "  dev-down  - 停止开发环境服务"
	@echo "  dev-logs  - 查看开发环境日志"

# 生产环境命令
build:
	docker-compose build

up:
	docker-compose up -d

down:
	docker-compose down

logs:
	docker-compose logs -f website

restart:
	docker-compose restart

# 开发环境命令
dev:
	docker-compose -f docker-compose.dev.yml up -d

dev-down:
	docker-compose -f docker-compose.dev.yml down

dev-logs:
	docker-compose -f docker-compose.dev.yml logs -f website-dev

# 清理命令
clean:
	docker-compose down -v
	docker system prune -f
	docker volume prune -f
