# Docker 管理命令

.PHONY: help build up down logs restart clean

# 默认目标
help:
	@echo "可用命令:"
	@echo "  build     - 构建 Docker 镜像"
	@echo "  up        - 启动服务"
	@echo "  down      - 停止服务"
	@echo "  logs      - 查看日志"
	@echo "  restart   - 重启服务"
	@echo "  clean     - 清理 Docker 资源"

# Docker 命令
build:
	docker-compose build

up:
	docker-compose up -d

down:
	docker-compose down

logs:
	docker-compose logs -f website

restart:
	docker-compose restart

# 清理命令
clean:
	docker-compose down -v
	docker system prune -f
	docker volume prune -f
