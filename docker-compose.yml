version: '3.8'

services:
  website:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "3100:3100"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    container_name: ray-blog
    volumes:
      # 可选：如果需要持久化日志或其他数据
      - ./logs:/app/logs
    networks:
      - website-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3100"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  website-network:
    driver: bridge
