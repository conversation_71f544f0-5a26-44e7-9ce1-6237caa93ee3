/* hero */
.hero {
  height: calc(100vh - 120px);
  width: 100vw;
  max-width: 100%;
  margin: 0;
  display: grid;
  grid-template-columns: 8fr 11fr;
  align-items: center;
  position: relative;
  letter-spacing: 0.04em;
  padding: 0;
}

.intro {
  padding: 1rem;
  padding-left: 4rem;
  position: relative;
  z-index: 10;
}

.intro > p {
  margin: 1.5rem 0;
  color: #6e7b8c;
  text-align: justify;
  font-size: 1rem;
  line-height: 2rem;
  letter-spacing: -0.04em;
  text-shadow: 0 0 #8c99ab;
}

.hero_text {
  font-size: calc(1.5em + 1.2vw);
}

.name {
  --lighting-size: 300px;
  --lighting-color: #007acc;
  --lighting-highlight-color: #4da6ff;

  background-image: radial-gradient(
    var(--lighting-highlight-color),
    var(--lighting-color),
    var(--lighting-color)
  );
  background-size: var(--lighting-size) var(--lighting-size);
  background-repeat: no-repeat;
  background-position-x: calc(
    var(--x) - var(--mouse-x) - calc(var(--lighting-size) / 2)
  );
  background-position-y: calc(
    var(--y) - var(--mouse-y) - calc(var(--lighting-size) / 2)
  );
  background-color: var(--lighting-color);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
}

.background {
  position: relative;
  width: 100%;
  height: 80%;
  display: grid;
  place-items: center;
  align-items: start;
  z-index: 5;
  overflow: hidden;
}

.background svg {
  width: 90%;
  height: 90%;
  max-height: 90%;
  max-width: 90%;
  object-fit: contain;
}

.circle {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(150, 255, 244, 0.81), rgba(0, 71, 252, 0.81));
  border-radius: 50%;
  opacity: 0.3;
  filter: blur(80px);
  z-index: -1;
}

.box {
  position: absolute;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  background: transparent;
  color: transparent;
  backdrop-filter: blur(2px);
  box-shadow: inset 1px 1px 5px rgba(255, 255, 255, 0.3), 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 0.5rem;
  padding: 0.5rem;
  width: 3.5rem;
  height: 3.5rem;
}

.buttonContainer {
  margin-top: 1rem;
  display: flex;
  gap: 0.5rem;
}

.introButton {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  border-radius: 1rem;
  border: 1px solid #e5e5e5;
  background: #ffffff;
  padding: 0.75rem 1.25rem;
  text-align: center;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.2s ease;
}

.introButton:hover {
  background: #f8f9fa;
  border-color: #007acc;
}

.buttonLink {
  font-weight: 600;
  text-decoration: none;
  color: #333333;
}

.buttonLink:hover {
  color: #007acc;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .introButton {
    background: #1a1a1a;
    border-color: #404040;
  }

  .introButton:hover {
    background: #2a2a2a;
    border-color: #4da6ff;
  }

  .buttonLink {
    color: #ffffff;
  }

  .buttonLink:hover {
    color: #4da6ff;
  }
}

@media (max-width: 1000px) {
  .hero {
    grid-template-columns: 1fr;
    grid-template-rows: max-content minmax(0, max-content);
    align-items: start;
    height: auto;
  }

  .intro {
    padding: 0;
    padding-top: 4rem;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .intro > p {
    padding: 0 1rem;
  }

  .background {
    width: 100%;
    justify-self: center;
    padding-top: 4rem;
    height: 100%;
    display: grid;
    place-items: center;
  }

  .background svg {
    width: 90%;
    height: auto;
  }

  .box {
    width: 3rem;
    height: 3rem;
  }
}

@media (max-width: 570px) {
  .hero {
    height: auto;
  }

  .background {
    padding-top: 2rem;
  }

  .background svg {
    width: 100%;
    height: auto;
  }

  .box {
    width: 2rem;
    height: 2rem;
  }

  .intro {
    padding-top: 2rem;
  }
}
